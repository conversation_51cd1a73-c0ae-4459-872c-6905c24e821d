package com.miniot.wuyou.controller;

import com.miniot.wuyou.common.R;
import com.miniot.wuyou.service.OSSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Bucket管理控制器
 */
@RestController
@RequestMapping("/api/bucket")
@Slf4j
public class BucketController {

    @Autowired
    private OSSService ossService;

    /**
     * 创建新的Bucket
     */
    @PostMapping("/create")
    public R<Boolean> createBucket(@RequestParam("bucketName") String bucketName) {
        log.info("开始创建Bucket: {}", bucketName);
        boolean result = ossService.createBucket(bucketName);
        return result ? 
            R.success(true, "创建Bucket成功") : 
            R.error("创建Bucket失败");
    }

    /**
     * 获取所有Bucket列表
     */
    @GetMapping("/list")
    public R<List<String>> listBuckets() {
        log.info("开始获取Bucket列表");
        List<String> buckets = ossService.listBuckets();
        return R.success(buckets, "获取Bucket列表成功");
    }
} 