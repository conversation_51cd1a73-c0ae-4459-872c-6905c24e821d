package com.miniot.wuyou.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回结果类
 */
@Data
public class R<T> implements Serializable {

    private Integer code; // 状态码 1 成功 0 失败
    private String msg; // 错误信息
    private T data; // 数据

    public static <T> R<T> success(T data) {
        R<T> r = new R<>();
        r.code = 1;
        r.msg = "操作成功";
        r.data = data;
        return r;
    }

    public static <T> R<T> success(T data, String msg) {
        R<T> r = new R<>();
        r.code = 1;
        r.msg = msg;
        r.data = data;
        return r;
    }

    public static <T> R<T> error(String msg) {
        R<T> r = new R<>();
        r.code = 0;
        r.msg = msg;
        return r;
    }

    public static <T> R<T> error(Integer code, String msg) {
        R<T> r = new R<>();
        r.code = code;
        r.msg = msg;
        return r;
    }
} 