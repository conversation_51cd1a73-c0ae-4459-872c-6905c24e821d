**使用条件**

- **前提条件**

用户需要具备以下条件，然后才能够使用 OSS Java SDK：

1. 拥有一个联通云账号，并开通对象存储OSS服务
2. 安装JDK 8或以上版本

- **获取访问凭证**

AccessKeyId和SecretKey是用户访问OSS的密钥，OSS会通过它来验证用户的资源请求，请妥善保管。您可以从官网页面左上角的用户头像-访问控制中，选择要创建密钥的用户，点击安全设置-新增访问密钥。

- **API手册**

开发者在使用SDK前请务必首先阅读联通云对象存储OSS API文档，以便掌握各请求参数和响应参数的使用方法。

- **安装SDK**

联通云对象存储OSS兼容Amazon S3接口，因此您可以使用S3的官方SDK调用联通云OSS的API接口。

在Maven工程中使用S3 Java SDK，只需在pom.xml中加入相应依赖即可。以1.11.24版本为例，在<dependencies>中加入如下内容：

<dependency>  <groupId>com.amazonaws</groupId>  <artifactId>aws-java-sdk-core</artifactId>  <version>1.11.24</version></dependency><dependency>  <groupId>com.amazonaws</groupId>  <artifactId>aws-java-sdk-s3</artifactId>  <version>1.11.24</version></dependency>

**SDK使用设置**

- **基础设置**

使用Java SDK访问OSS服务，需要设置正确的AccessKeyId、SecretKey和服务端地址Endpoint。

- **创建AmazonS3Client对象**

以下代码用于创建AmazonS3Client对象：

public class S3Sample {  private final static String END_POINT = "https://obs-helf.cucloud.cn";//请替换成您Bucket所在区域的endpoint  public static void main(String[] args) throws IOException {  BasicAWSCredentials basicAwsCred = new BasicAWSCredentials("Your AccessKey", "Your SecretKey"); //请替换成您的AccessKey和SecretKey  ClientConfiguration clientConfiguration = new ClientConfiguration();    clientConfiguration.setProtocol(Protocol.HTTPS);    AmazonS3Client s3 = new AmazonS3Client(basicAwsCred,clientConfiguration);    S3ClientOptions options = S3ClientOptions.builder().setPathStyleAccess(true).setPayloadSigningEnabled(true).disableChunkedEncoding().build();    s3.setS3ClientOptions(options);    s3.setEndpoint(ENDPOINT);}

**关于Service的操作**

- **GET Service(List Bucket)**

此操作返回请求者拥有的所有 Bucket。只有根用户和具有GET Service 权限的子用户才能执行此操作。代码示例如下：

  public static void listBuckets(AmazonS3 ossClient){    List<Bucket> listBuckets = ossClient.listBuckets();//获取bucket名称、创建时间和用户信息    for (Bucket bucketInfo : listBuckets) {      System.out.println("listBuckets:" + "Name:" + bucketInfo.getName();            }  }

**关于Bucket的操作**

- **CreateBucket**

此操作可以用来创建一个新的 Bucket。只有根用户和具有 PUT Bucket 权限的子用户才能创建 Bucket。代码示例如下：

public class S3Sample {  private final static String END_POINT = "https://obs-helf.cucloud.cn";//请替换成您Bucket所在区域的endpoint  public static void main(String[] args) throws IOException {  BasicAWSCredentials basicAwsCred = new BasicAWSCredentials("Your AccessKey", "Your SecretKey"); //请替换成您的AccessKey和SecretKey  ClientConfiguration clientConfiguration = new ClientConfiguration();    clientConfiguration.setProtocol(Protocol.HTTPS);    AmazonS3Client s3 = new AmazonS3Client(basicAwsCred,clientConfiguration);    S3ClientOptions options = S3ClientOptions.builder().setPathStyleAccess(true).setPayloadSigningEnabled(true).disableChunkedEncoding().build();    s3.setS3ClientOptions(options);    s3.setEndpoint(END_POINT);     String bucketName = "mybucket"; //请替换成您的bucket名称String key = "myobject";    try {      System.out.println("Creating bucket " + bucketName + "\n");      s3.createBucket(bucketName);      System.out.println("Creating bucket successful");    } catch (AmazonServiceException ase) {      System.out.println("Caught an AmazonServiceException, which means your request made it "          + "to Amazon S3, but was rejected with an error response for some reason.");      System.out.println("Error Message:   " + ase.getMessage());      System.out.println("HTTP Status Code: " + ase.getStatusCode());      System.out.println("AWS Error Code:  " + ase.getErrorCode());      System.out.println("Error Type:    " + ase.getErrorType());      System.out.println("Request ID:    " + ase.getRequestId());    } catch (AmazonClientException ace) {      System.out.println("Caught an AmazonClientException, which means the client encountered "          + "a serious internal problem while trying to communicate with OSS, "          + "such as not being able to access the network.");      System.out.println("Error Message: " + ace.getMessage());    }  }}

- **DeleteBucket**

此操作用来删除 Bucket，并要求被删除的 Bucket 中没有 Object，即该 Bucket 中的所有对象和分片都已被删除。代码示例如下：

public static void deleteBucket(AmazonS3 ossClient){myBucketName = "test-example";   ossClient.deleteBucket(myBucketName);}

- **HeadBucket**

此操作用于判断 Bucket 是否存在，而且用户是否有权限访问。如果Bucket 存在，而且用户有权限访问，则此操作返回 200 OK。否则返回 404 不存在，或者 403 没有权限。代码示例如下：

// bucket存在public static void headBucket(AmazonS3 ossClient) {HeadBucketRequest request = new HeadBucketRequest(myBucketName);HeadBucketResult result = ossClient.headBucket(request);System.out.println(result.getBucketRegion());}  //bucket不存在public static void headBucketNotExist(AmazonS3 ossClient) {try {HeadBucketRequest request = new HeadBucketRequest("no-existbucket");ossClient.headBucket(request);} catch (AmazonS3Exception e) {System.out.println(e.getMessage());}}

- **GetBucket(ListObjects)**

此接口用来返回 Bucket 中部分或者全部（最多 1000）的 Object 信息。用户可以在请求元素中设置选择条件来获取 Bucket 中的 Object 的子集。只有根用户和拥有 GET Bucket 权限的子用户才能执行此操作。代码示例如下： 

public static void listObjects(AmazonS3 s3) {System.out.println("Listing objects");   ObjectListing objectListing = s3.listObjects(new ListObjectsRequest()          .withBucketName(bucketName)          .withPrefix("My"));   for (S3ObjectSummary objectSummary : objectListing.getObjectSummaries()) {    System.out.println(" - " + objectSummary.getKey() + "  " +                  "(size = " + objectSummary.getSize() + ")");   }  }

- **GetBucketLocation**

此操作用来获取 Bucket 所在的地域，只有根用户和拥有 GET Bucket location 权限的子用户才能执行此操作。代码示例如下：

public static void getBucketLocation(AmazonS3 ossClient) {myBucketName = "test-example";String locationResult = ossClient.getBucketLocation(myBucketName);//获取bucket的位置信息System.out.println("getBucketLocation:" + locationResult);}

- **PutBucketACL**

此接口用来设置Bucket的访问权限。只有根用户和拥有Put Bucket ACL权限的子用户才能执行此操作。代码示例如下：

public static void putBucketAcl(AmazonS3 ossClient) {//设置bucket属性为私有CannedAccessControlList aclReq = CannedAccessControlList.Private;SetBucketAclRequest bucketACLReq = new SetBucketAclRequest(bucket, aclReq);ossClient.setBucketAcl(bucketACLReq);}

- **GetBucketACL**

此接口用来获取Bucket的访问权限。只有根用户和拥有Get Bucket ACL权限的子用户才能执行此操作。代码示例如下：

public static void getBucketAcl(AmazonS3 ossClient){ myBucketName = "test-example"; List<Grant> acl = ossClient.getBucketAcl(myBucketName).getGrantsAsList();//获取用户标识和权限信息 for (int i=0;i<acl.size();i++)   System.out.println("grantee: "+acl.get(i).getGrantee().getIdentifier() + " permission: "+acl.get(i).getPermission());} 

- **PutBucketVersioning**

设置Bucket的多版本配置。如果配置已经存在，OSS 会覆盖它。只有根用户和拥有 PUT Bucket Versioning权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void putBucketVersion(AmazonS3 ossClient) {BucketVersioningConfiguration versionConf = new BucketVersioningConfiguration();//开启bucket的多版本控制versionConf.setStatus("Enabled");SetBucketVersioningConfigurationRequest versionReq = new SetBucketVersioningConfigurationRequest(myBucketName, versionConf);ossClient.setBucketVersioningConfiguration(versionReq);}

- **GetBucketVersioning**

获取Bucket的多版本配置。只有根用户和拥有 GET Bucket Versioning权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void getBucketVersion(AmazonS3 ossClient) {BucketVersioningConfiguration result = ossClient.getBucketVersioningConfiguration(myBucketName);System.out.println("Status: " + result.getStatus());}

- **ListObjectVersions**

此接口用于列出Bucket中包括删除标记（Delete Marker）在内的所有Object的版本信息。代码示例如下：

public static void listVersions(AmazonS3 ossClient) {ListVersionsRequest listVersionReq = new ListVersionsRequest();listVersionReq.setBucketName(myBucketName);//设置list versions时的key markerlistVersionReq.setKeyMarker("v");VersionListing result = ossClient.listVersions(listVersionReq);//获取对象版本信息，包括对象名称、对象的版本ID、对象大小for (int i = 0; i < result.getVersionSummaries().size(); i++)System.out.println("key: " + result.getVersionSummaries().get(i).getKey() + " versionId: "+ result.getVersionSummaries().get(i).getVersionId() + " size: "+ result.getVersionSummaries().get(i).getSize());}

- **PutBucketCors**

设置Bucket的跨域配置。如果配置已经存在，OSS 会覆盖它。只有根用户和拥有 PUT Bucket CORS 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。在配置跨域请求时，用户可以配置允许跨域的源和 HTTP 方法。 最多可以配置100条跨域规则，且XML 请求体不能超过64KB。代码示例如下：

public static void putBucketCors(AmazonS3 ossClient) {BucketCrossOriginConfiguration corsConfig = new BucketCrossOriginConfiguration();/设置跨域规则，包括允许的请求头、允许的方法、允许的源、暴露的响应头等信息List<CORSRule> rules = new ArrayList<CORSRule>();CORSRule rule = new CORSRule();rule.setAllowedHeaders("*");rule.setAllowedMethods(AllowedMethods.*GET*, AllowedMethods.*PUT*);rule.setAllowedOrigins("http://www.example.com");rule.setMaxAgeSeconds(3000);rule.setExposedHeaders("x-amz-version-id");rules.add(rule);corsConfig.setRules(rules);ossClient.setBucketCrossOriginConfiguration(*bucket*, corsConfig);}

- **GetBucketCors**

返回 Bucket 的跨域配置信息。只有根用户和拥有 GET Bucket CORS 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void getBucketCors(AmazonS3 ossClient) {BucketCrossOriginConfiguration cors = ossClient.getBucketCrossOriginConfiguration(myBucketName);for (int i = 0; i < cors.getRules().size(); i++) {//获取跨域配置的允许请求头for (int j = 0; j < cors.getRules().get(i).getAllowedHeaders().size(); j++)System.out.println("allow headers: " + cors.getRules().get(i).getAllowedHeaders().get(j));//获取跨域配置的允许请求方法for (int j = 0; j < cors.getRules().get(i).getAllowedMethods().size(); j++)System.out.println("allow methods: " + cors.getRules().get(i).getAllowedMethods().get(j));//获取跨域配置的允许Originfor (int j = 0; j < cors.getRules().get(i).getAllowedOrigins().size(); j++)System.out.println("allow origins: " + cors.getRules().get(i).getAllowedOrigins().get(j));//获取跨域配置允许暴露的响应头for (int j = 0; j < cors.getRules().get(i).getExposedHeaders().size(); j++)System.out.println("allow exposed headers: " + cors.getRules().get(i).getExposedHeaders().get(j)); }}

- **DeleteBucketCors**

删除 Bucket 的跨域配置信息。只有根用户和拥有 DELETE Bucket CORS 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void deleteBucketCors(AmazonS3 ossClient){  ossClient.deleteBucketCrossOriginConfiguration(myBucketName);}

- **PutBucketWebsit**

此操作用于设置bucket的静态网站托管规则并设置跳转策略。如果 Bucket 已经配置了静态网站托管，此操作会替换原有配置。只有根用户和拥有 PUT Bucket WebSite 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void putBucketWebsite(AmazonS3 ossClient){   BucketWebsiteConfiguration configuration = new BucketWebsiteConfiguration();//设置静态网站托管的默认主页和默认404页   configuration.setIndexDocumentSuffix("index.html");   configuration.setErrorDocument("error.html");   ossClient.setBucketWebsiteConfiguration(myBucketName, configuration); }



- **GetBucketWebsite**

此操作用于获取Bucket静态网站配置。只有根用户和拥有 GET Bucket WebSite 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void getBucketWebsite(AmazonS3 ossClient) {BucketWebsiteConfiguration result = ossClient.getBucketWebsiteConfiguration(myBucketName);// 获取静态托管网站主页和404页面System.out.println("index document: " + result.getIndexDocumentSuffix() + " error document:" + result.getErrorDocument());} 

- **DeleteBucketWebsite**

删除Bucket的静态网站托管配置。只有根用户和拥有 DELETE Bucket WebSite 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下：

public static void deleteBucketWebsite(AmazonS3 ossClient){  ossClient.deleteBucketWebsiteConfiguration(myBucketName);}

**PutBucketLifecycle**

此操作用来设置 Bucket 生命周期规则。只有根用户和具有 PUT Bucket Lifecycle权限的子用户才能执行此操作。代码示例如下： 

public static void putBucketLifecycle(AmazonS3 ossClient) {BucketLifecycleConfiguration lifeconfig = new BucketLifecycleConfiguration();List<Rule> lifeRules = new ArrayList<Rule>();Rule lifeRule = new Rule();lifeRule.setId("test"); // 设置规则，所有以logs/开头的对象和未合并片段，在10天后过期Rule r1 = new Rule();r1.setId("delete objects and parts with prefix 'logs/' after ten days");LifecycleFilter filter1 = new LifecycleFilter();LifecycleFilterPredicate pre1 = new LifecyclePrefixPredicate("logs/");filter1.setPredicate(pre1);r1.setFilter(filter1);AbortIncompleteMultipartUpload abortUpload = new AbortIncompleteMultipartUpload();abortUpload.setDaysAfterInitiation(10);r1.setAbortIncompleteMultipartUpload(abortUpload);r1.setStatus("Enabled");r1.setExpirationInDays(10);lifeRules.add(r1); lifeconfig.setRules(lifeRules);ossClient.setBucketLifecycleConfiguration(bucket, lifeconfig);}

- **GetBucketLifecycle**

此接口用于获取生命周期规则配置。代码示例如下：

public static void getBucketLifecycle(AmazonS3 ossClient) {BucketLifecycleConfiguration result = ossClient.getBucketLifecycleConfiguration(myBucketName);//获取生命周期的配置信息，包括对象过期时间，未合并片段过期时间for (int i = 0; i < result.getRules().size(); i++) {System.out.println("expirationInDays: " + result.getRules().get(i).getExpirationInDays());System.out.println("abortMultipartUploadDays: " + result.getRules().get(i).getAbortIncompleteMultipartUpload().getDaysAfterInitiation());}}

- **DeleteBucketeLifecycle**

删除指定存储空间（Bucket）的生命周期规则。代码示例如下：

public static void deleteBucketLifecycle(AmazonS3 ossClient) {ossClient.deleteBucketLifecycleConfiguration(myBucketName);}

- **PutBucketLogging**

此操作可以添加/修改/删除Bucket的日志配置。如果 Bucket 已经存在了生命周期配置，此操作会替换原有配置。只有根用户和拥有PUT Bucket Logging 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下： 

public static void putBucketLogging(AmazonS3 ossClient) {BucketLoggingConfiguration loggingConfiguration = new BucketLoggingConfiguration();//设置log日志的前缀loggingConfiguration.setLogFilePrefix("prefix-1");//设置log存储的目标BucketloggingConfiguration.setDestinationBucketName(myBucketName);SetBucketLoggingConfigurationRequest setBucketLoggingConfigurationRequest = new SetBucketLoggingConfigurationRequest(myBucketName, loggingConfiguration);ossClient.setBucketLoggingConfiguration(setBucketLoggingConfigurationRequest);}

- **GetBucketLogging**

此接口可以获得指定 Bucket 的日志配置。只有根用户和拥有 GET Bucket Logging 权限的子用户才能执行此操作，否则会返回 403 AccessDenied 错误。代码示例如下： 

public static void getBucketLogging(AmazonS3 ossClient) {BucketLoggingConfiguration logging = ossClient.getBucketLoggingConfiguration(myBucketName);//获取目标bucket名称和日志前缀System.out.println("destBucket: " + logging.getDestinationBucketName() + " logPrefix: " + logging.getLogFilePrefix());}

- ### PutBucketPolicy

设置Bucket的策略。如果配置已经存在，OSS 会覆盖它。只有根用户和拥有PUT Bucket Policy权限的子用户才能执行此操作，否则会返回403 AccessDenied错误。代码示例如下：



public static void putBucketPolicy(AmazonS3 ossClient) {String bucketName="testbucket";Policy bucketPolicy = new Policy().withStatements( new Statement(Statement.Effect.Allow)//设置权限为允许 .withPrincipals(Principal.AllUsers)//设置策略对所有用户生效 .withActions(S3Actions.GetObject)//设置操作为GetObject .withConditions(new StringCondition(StringCondition.StringComparisonType.StringLike, "aws:Referer", "http://test.com/")) .withResources(new Resource("arn:aws:s3:::" + bucketName + "/*")));//指定资源为Bucket内所有对象ossClient.setBucketPolicy(bucketName, bucketPolicy.toJson());}





- **GetBucketPolicy**

获取Bucket的策略。如果配置已经存在，OSS会覆盖它。只有根用户和拥有GET Bucket Policy权限的子用户才能执行此操作，否则会返回403 AccessDenied错误。代码示例如下： 

public static void getBucketPolicy(AmazonS3 ossClient) {BucketPolicy result = ossClient.getBucketPolicy(bucketName);System.out.println(result.getPolicyText());}

- ### DeleteBucketPolicy

删除Bucket的策略。如果配置已经存在，OSS会覆盖它。只有根用户和拥有DELETE  Bucket Policy权限的子用户才能执行此操作，否则会返回403 AccessDenied错误。代码示例如下：

public static void deleteBucketPolicy(AmazonS3 ossClient) {ossClient.deleteBucketPolicy(bucket);}

**关于Object的操作**

- **PutObject**

Put Object操作用来向指定Bucket中添加一个对象，要求发送请求者对该Bucket拥有写权限，用户必须添加完整的对象。代码示例如下：

public static void putObject(AmazonS3 s3) {System.out.println("Uploading a new object to S3 from a file\n");  s3.putObject(    new PutObjectRequest(bucketName, key, createSampleFile())          .withCannedAcl(CannedAccessControlList.PublicRead));}  /**   * Creates a temporary file with text data to demonstrate uploading a file   * to OSS   *   * @return A newly created temporary file with text data.   *   * @throws IOException   */ private static File createSampleFile() throws IOException {    File file = File.createTempFile("oss-java-sdk-", ".txt");    file.deleteOnExit();    Writer writer = new OutputStreamWriter(new FileOutputStream(file));    writer.write("abcdefghijklmnopqrstuvwxyz\n");    writer.write("01234567890112345678901234\n");    writer.write("!@#$%^&*()-=[]{};':',.<>/?\n");    writer.write("01234567890112345678901234\n");    writer.write("abcdefghijklmnopqrstuvwxyz\n");    writer.close();    return file; }

上传对象时，也可以指定对象属性信息，代码示例如下：

private static void putObject(AmazonS3 client, String bucketName, String objectName) {    PutObjectRequest request = new PutObjectRequest(bucketName, objectName, new File("d:\\1.txt"));    ObjectMetadata metadata = new ObjectMetadata();    metadata.setCacheControl("no-cache");    metadata.setContentDisposition("attachement");    metadata.setContentEncoding("gzip");    metadata.setContentType("text/plain");    Map<String, String> userMetadata = new HashMap<String, String>();    userMetadata.put("key1", "value1");    metadata.setUserMetadata(userMetadata);    request.setMetadata(metadata);    client.putObject(request);    System.out.println("Put object successfully");  }

- **GetObject**

此操作用来获取在 OSS 中的对象，执行此操作，用户必须对 Object 所在的 Bucket 有读权限。如果 Bucket 是 public read 的权限，匿名用户也可以通过非授权的方式进行读操作。代码示例如下：

public static void getObject(AmazonS3 s3) {System.out.println("Downloading an object");  S3Object object =ossClient.getObject(new GetObjectRequest(bucketName, key));System.out.println("Content-Type: "  + object.getObjectMetadata().getContentType());  displayTextInputStream(object.getObjectContent());} /**   * Displays the contents of the specified input stream as text.   *   * @param input   *       The input stream to display as text.   *   * @throws IOException   */private static void displayTextInputStream(InputStream input) throws IOException {  BufferedReader reader = new BufferedReader(new InputStreamReader(input));  while (true) {    String line = reader.readLine();    if (line == null) break;    System.out.println("   " + line);  }  System.out.println();}

- **DeleteObject**

此接口用于删除指定的对象。代码示例如下：

public static void deleteObject(AmazonS3 ossClient) {String myKeyName = "1.txt";ossClient.deleteObject(myBucketName, myKeyName);}

- **HeadObject**

此操作用于获取对象的元数据信息，而不返回数据本身。当只希望获取对象的属性信息时，可以使用此操作。代码示例如下：

public static void headObject(AmazonS3 ossClient) {String myKeyName = "1.txt";ObjectMetadata metadata = ossClient.getObjectMetadata(*myBucketName*, myKeyName);//获取对象的属性信息，包括Cache-Control、Content-Disposition、Content-Encoding、Content-Type、VersionId等System.out.println("Cache-Control: " + meta.getCacheControl());System.out.println("Content-Disposition: " + meta.getContentDisposition());System.out.println("Content-Encoding: " + meta.getContentEncoding());System.out.println("Content-Type: " + meta.getContentType());System.out.println("VersionId: " + meta.getVersionId());}

- **PUT Object - Copy**

此接口用于拷贝对象，或修改对象属性。要执行拷贝请求，用户需要对源对象有读权限，对目标对象有写权限。代码示例如下：

//拷贝生成一个新的对象public static void copyObject(AmazonS3 ossClient) {//设置源bucket和源对象名称String sourceBucketName = myBucketName;String sourceKey = "1.txt";//设置目标bucket和目标对象名称String destinationBucketName = bucket;String destinationKey = "3.txt";CopyObjectRequest request = new CopyObjectRequest(sourceBucketName, sourceKey, destinationBucketName, destinationKey);ObjectMetadata metadata = new ObjectMetadata();// 设置目标对象的Cache-Control属性metadata.setCacheControl("cache");// 设置目标对象的Content-Disposition属性metadata.setContentDisposition("attachement");// 设置目标对象的Content-Encoding属性metadata.setContentEncoding("gzip");// 设置目标对象的Content-Type属性metadata.setContentType("text/plain");// 设置目标对象的权限为privatemetadata.setHeader("x-amz-acl", "private");// 设置目标对象的自定义元数据Map<String, String> userMeta = new HashMap<String, String>();userMeta.put("Key1", "value1");userMeta.put("Key2", "value2");metadata.setUserMetadata(userMeta);request.setNewObjectMetadata(metadata);ossClient.copyObject(request);System.out.println("destination key ETag: "+result.getETag());}

- **PutObjectACL**

此接口用于设置对象的访问权限。代码示例如下：

public static void putObjectAcl(AmazonS3 ossClient) {String myKeyName = "1.txt";//设置对象属性为公开读SetObjectAclRequest objecAclReq = new SetObjectAclRequest(myBucketName, myKeyName, CannedAccessControlList.PublicRead);ossClient.setObjectAcl(objecAclReq);}

- **GetObjectACL**

此接口用于获取对象的访问权限。代码示例如下：

public static void getObjectAcl(AmazonS3 ossClient) {String myKeyName = "1.txt";List<Grant> acl = ossClient.getObjectAcl(myBucketName, myKeyName).getGrantsAsList();//获取用户标识和权限信息for (int i = 0; i < acl.size(); i++)System.out.println("grantee: " + acl.get(i).getGrantee().getIdentifier() + " permission: " + acl.get(i).getPermission());}

- **CreateMultipartUpload**

本接口初始化一个分片上传（Multipart Upload）操作，并返回一个上传 ID，此ID 用来将此次分片上传操作中上传的所有片段合并成一个对象。用户在执行每一次上传请求（UploadPart）时都必须指定该 ID，在合并分片操作（CompleteMultipartUpload）和终止分片上传操作（AbortMultipartUpload）时也需要指定该 ID。代码示例如下：

public static void initiateMultipartUpload(AmazonS3 ossClient) {// 1、Initiate the multipart upload.InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, key);  InitiateMultipartUploadResult res = ossClient.initiateMultipartUpload(request);  //获取初始化分片的UploadID  System.out.println("uploadID:" +  res.getUploadId());}

初始化分片时，也可以指定对象的属性信息，代码示例如下：

private static void multipartUpload(AmazonS3 client, String bucketName, String objectName2) {     InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, objectName2);    ObjectMetadata metadata = new ObjectMetadata();    metadata.setCacheControl("no-cache");    metadata.setContentDisposition("attachement");    metadata.setContentEncoding("gzip");    metadata.setContentType("text/plain");    Map<String, String> userMetadata = new HashMap<String, String>();    userMetadata.put("key1", "value1");    metadata.setUserMetadata(userMetadata);    request.setObjectMetadata(metadata);    InitiateMultipartUploadResult result = client.initiateMultipartUpload(request);  }



- **UploadPart**

该接口用于上传分片。在上传任何一个分片之前，必须执行 Initial Multipart Upload 操作来初始化分片上传，初始化成功后，OSS 会返回一个上传 ID，这是一个唯一的标识，用户必须在调用 Upload Part 接口时加入该 ID。

分片号 PartNumber 可以唯一标识一个片段并且定义该分片在对象中的位置，范围从 1 到 10000。如果用户用之前上传过的片段的分片号来上传新的分片，之前的分片将会被覆盖。

所有分片的都不应小于 5M（除最后一个分片外），且不大于5GB。为了确保数据不会由于网络传输而毁坏，需要在每个分片上传请求中指定Content-MD5 头，OSS 通过提供的 Content-MD5 值来检查数据的完整性，如果不匹配，则会返回一个错误信息。代码示例如下：

public static void uploadPart(AmazonS3 ossClient) {for (int i = 1; filePosition < contentLength; i++) {    // Because the last part could be less than 5 MB, adjust the part size as needed.    partSize = Math.min(partSize, (contentLength - filePosition));    // Create the request to upload a part.    UploadPartRequest uploadRequest = new UploadPartRequest()        .withBucketName(bucketName)        .withKey(key)        .withUploadId(res.getUploadId())        .withPartNumber(i)        .withFileOffset(filePosition)        .withFile(file)        .withPartSize(partSize);     // Upload the part and add the response's ETag to our list.     UploadPartResult uploadResult = ossClient.uploadPart(uploadRequest);     partETags.add(new PartETag(1, uploadResult.getETag()));     System.out.println("ETag: " + uploadResult.getETag());     filePosition += partSize;   }}

- **CompleteMultipartUpload**

该接口将所有分片合成一个完整的对象并删除分片。用户首先调用Create Multipart Upload接口初始化分片上传，然后通过 Upload Part 接口上传所有分片。在将所有片段都上传成功之后，调用Complete Multipart Upload接口来结束分片上传过程。当OSS收到这个请求时，会以分片号升序排列的方式将所有片段依次拼接来创建一个新的对象。在Complete Multipart Upload 请求中，用户需要提供一个片段列表，这个片段列表中的所有片段都必须已经上传完成，并提供所有片段上传完成时返回的 ETag和对应的分片号。代码示例如下：

public static void completeMultipartUpload(AmazonS3 ossClient) {CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(bucketName, key,  res.getUploadId(),partETags);  ossClient.completeMultipartUpload(compRequest);}

- **AbortMultipartUpload**

该接口用于终止一次分片上传操作。分片上传操作被终止后，用户不能再通过上传 ID 上传其它片段，之前已上传完成的片段所占用的存储空间将被释放。如果此时任何片段正在上传，该上传过程可能会也可能不会成功。所以，为了释放所有片段所占用的存储空间，可能需要多次终止分片上传操作。代码示例如下：

public static void abortMultipartUpload(AmazonS3 ossClient) {String myKeyName = "1.txt";//设置bucket名称，对象名称和UploadIDAbortMultipartUploadRequest request = new AbortMultipartUploadRequest(myBucketName, myKeyName,"ca2b18bc30b3ab30c8a44f35e84284131e7e526d8341c434");ossClient.abortMultipartUpload(request);}

- **CopyPart**

可以将已经存在的Object作为分段上传的片段，拷贝生成一个新的片段。需要指定请求头x-amz-copy-source来定义拷贝源。如果只拷贝源Object中的一部分，需要增加请求头x-amz-copy-source-range。代码示例如下：

public static void copyPart(AmazonS3 ossClient) {CopyPartRequest request = new CopyPartRequest();// 设置拷贝的源bucket和对象名称request.setSourceBucketName(myBucketName);request.setSourceKey("1.zip");// 设置拷贝的目标bucket和对象名称request.setDestinationBucketName("destinationbucket");request.setDestinationKey("2.zip");// 设置Copy Part范围（如果要拷贝整个object，不需要设置）。// 如果设置范围，必须同时设置request.setFirstByte和request.setLastByte。request.setFirstByte(0L); // Copy Part起始字段。request.setLastByte(1000L); // Copy Part结束字段。  //设置part numberrequest.setPartNumber(1);request.setUploadId("ca2b18bc30b3ab30c8a44f35e84284131e7e526d8341c434");CopyPartResult result = ossClient.copyPart(request);// 获取拷贝后的ETagSystem.out.println("ETag: " + result.getETag());}

- **ListMultipartUploads**

该接口用于列出所有已经通过Initiate Multipart Upload请求初始化，但未完成或未终止的分片上传过程。响应中最多返回1000个分片信息，用户也可以通过设置max-uploads参数来限制响应返回中的分片个数。如果当前的分片数超出了这个值，则响应中会包含一个值为true的IsTruncated元素。如果用户要列出多于max-uploads值的分片信息，则需要继续调用List Multipart Uploads请求，并在请求中设置key-marker和upload-id-marker参数。

在响应体中，分片上传过程的信息通过key来排序。如果用户的应用程序中启动了多个使用同一key对象名称开头的分片上传过程，那么响应体中分片上传过程首先是通过key来排序，在相同key的分片上传内部则是按上传启动的起始时间的升序来进行排列。代码示例如下：

public static void listMultipartUploads(AmazonS3 ossClient) {ListMultipartUploadsRequest listMultipartUploadsRequest = new ListMultipartUploadsRequest(myBucketName);MultipartUploadListing result = ossClient.listMultipartUploads(listMultipartUploadsRequest);//获取分片上传对象的名称、uploadID、初始化时间for (int i = 0; i < result.getMultipartUploads().size(); i++)System.out.println("key: " + result.getMultipartUploads().get(i).getKey() + " uploadId: "+ result.getMultipartUploads().get(i).getUploadId() + " initiated: "+ result.getMultipartUploads().get(i).getInitiated());}

- **ListPart**

该操作用于列出一次分片上传过程中已经上传完成的所有片段。一次最多返回1000个分片信息，默认返回的分片数是1000。用户可以通过指定max-parts参数来指定一次请求返回的最大分片数。如果用户的分片数超过1000个，那么OSS会响应值为true的IsTruncated字段，并且返回一个NextPartNumberMarker元素。用户可以在下一个List Part请求中加入part-number-marker参数，并把它的值设置成上一个请求返回的NextPartNumberMarker值，用于列出后续的分片。代码示例如下：

public static void listParts(AmazonS3 ossClient) {String myKeyName = "1.txt";//获取指定bucket名称，对象名称和UploadID的分片ListPartsRequest request = new ListPartsRequest(myBucketName, myKeyName,"ca2b18bc30b3ab30c8a44f35e84284131e7e526d8341c434");PartListing result = ossClient.listParts(request);//获取分片的part number和ETagfor (int i = 0; i < result.getParts().size(); i++)System.out.println("partNum: " + result.getParts().get(i).getPartNumber() + " ETag: " + result.getParts().get(i).getETag());}

- **DeleteMultipleObjects**

批量删除 Object 功能支持用一个 HTTP 请求删除一个 Bucket 中的多个 Object。批量删除请求包含一个不超过 1000 个 Object的 XML 列表。在这个XML中，需要指定要删除的 Object名称。对于每个 Object，OSS 都会返回删除的结果，成功或者失败。注意，如果请求中的 Object不存在，那么 OSS 也会返回删除成功。

批量删除功能支持两种格式的响应，全面信息和简明信息。默认情况下，OSS在响应中会显示全面信息，即包含每个 object 的删除结果。在简明信息模式下，OSS 只返回删除出错的 Object的结果。对于成功删除的 Object，在响应中将不返回任何信息。代码示例如下：

public static void deleteObjects(AmazonS3 ossClient) {String myKeyName1 = "1.txt";String myKeyName2 = "2.txt";// 设置批量删除的对象名称和版本号DeleteObjectsRequest request = new DeleteObjectsRequest(myBucketName);DeleteObjectsRequest.KeyVersion key1 = new DeleteObjectsRequest.KeyVersion(myKeyName1, null);DeleteObjectsRequest.KeyVersion key2 = new DeleteObjectsRequest.KeyVersion(myKeyName2, null);List<DeleteObjectsRequest.KeyVersion> keys = new ArrayList<DeleteObjectsRequest.KeyVersion>();keys.add(key1);keys.add(key2);request.withKeys(keys);DeleteObjectsResult result = ossClient.deleteObjects(request);// 返回删除的对象信息for (int i = 0; i < result.getDeletedObjects().size(); i++)System.out.println("key: "+result.getDeletedObjects().get(i).getKey());}

- **生成共享链接**

对于私有或只读Bucket，可以通过生成Object的共享链接的方式，将Object分享给其他用户。代码示例如下：

public static void generatePresignedUrl(AmazonS3 ossClient) {String myKeyName = "1.txt";GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(myBucketName, myKeyName);   //设置链接过期时间request.setExpiration(DateUtils.parseDate("Fri, 31 Dec 2021 08:54:55 GMT"));//生成共享链接System.out.println(ossClient.generatePresignedUrl(request));}

- ### **通过URL上传对象** 

可以通过生成共享链接URL的方式上传对象，代码示例如下：

public static URL  generatePresignedUrl(AmazonS3 ossClient) {    GeneratePresignedUrlRequest request = new  GeneratePresignedUrlRequest("your-bucket-name", "your-object-name");  // 设置链接过期时间  request.setExpiration(DateUtils.parseDate("Sun,  31 Dec 2023 08:54:55 GMT"));  request.setMethod(HttpMethod.PUT);  // 生成共享链接  URL  url=ossClient.generatePresignedUrl(request);  System.out.println(url);  return url;}public static void  useHttpUrlConnectionToPutString(URL presignedUrl) {   try {    // Create the connection and use it  to upload the new object by using the presigned URL.    HttpURLConnection connection =  (HttpURLConnection) presignedUrl.openConnection();    connection.setDoOutput(true);    connection.setRequestMethod("PUT");    OutputStreamWriter out = new  OutputStreamWriter(connection.getOutputStream());    out.write("This text was uploaded  as an object by using a presigned URL.");    out.close();    System.out.println("HTTP response  code is " + connection.getResponseCode());    } catch (IOException e) {      e.printStackTrace();    }}