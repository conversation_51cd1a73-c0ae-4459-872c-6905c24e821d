<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .upload-container {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .upload-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background-color: #45a049;
        }
        .file-input {
            margin-bottom: 15px;
        }
        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 5px;
            margin: 10px 0;
        }
        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            border-radius: 5px;
            width: 0%;
            transition: width 0.3s;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
            display: none;
        }
        .video-preview {
            margin-top: 20px;
            max-width: 100%;
            display: none;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
        }
        .tab-content {
            display: none;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 0 5px 5px 5px;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>视频上传测试</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="showTab('direct-upload')">直接上传</div>
        <div class="tab" onclick="showTab('presigned-upload')">预签名URL上传</div>
    </div>
    
    <div id="direct-upload" class="tab-content active">
        <div class="upload-container">
            <h3>选择视频文件上传</h3>
            <input type="file" id="videoFile" class="file-input" accept="video/*">
            <div>
                <label for="directory">存储目录: </label>
                <input type="text" id="directory" value="videos">
            </div>
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <button class="upload-btn" onclick="uploadVideo()">上传视频</button>
        </div>
        
        <div class="result" id="result">
            <h3>上传结果</h3>
            <pre id="resultContent"></pre>
            <button class="upload-btn" onclick="deleteVideo()">删除视频</button>
        </div>
        
        <video class="video-preview" id="videoPreview" controls></video>
    </div>
    
    <div id="presigned-upload" class="tab-content">
        <div class="upload-container">
            <h3>使用预签名URL上传</h3>
            <div>
                <label for="objectKey">对象键: </label>
                <input type="text" id="objectKey" value="videos/test.mp4">
            </div>
            <div>
                <label for="expireTime">过期时间(秒): </label>
                <input type="number" id="expireTime" value="3600">
            </div>
            <button class="upload-btn" onclick="getPresignedUrl()">获取预签名URL</button>
            
            <div style="margin-top: 20px;">
                <input type="file" id="presignedFile" class="file-input" accept="video/*">
                <div class="progress-container">
                    <div class="progress-bar" id="presignedProgressBar"></div>
                </div>
                <button class="upload-btn" id="uploadPresignedBtn" onclick="uploadWithPresignedUrl()" disabled>使用预签名URL上传</button>
            </div>
        </div>
        
        <div class="result" id="presignedResult">
            <h3>预签名URL</h3>
            <pre id="presignedUrlContent"></pre>
            <h3>上传结果</h3>
            <pre id="presignedResultContent"></pre>
        </div>
    </div>
    
    <script>
        let uploadedObjectKey = '';
        let presignedUrl = '';
        
        function showTab(tabId) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 取消所有标签的激活状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选定的标签内容
            document.getElementById(tabId).classList.add('active');
            
            // 激活选定的标签
            const selectedTab = document.querySelector(`.tab[onclick="showTab('${tabId}')"]`);
            selectedTab.classList.add('active');
        }
        
        function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const directory = document.getElementById('directory').value || 'videos';
            const progressBar = document.getElementById('progressBar');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const videoPreview = document.getElementById('videoPreview');
            
            if (!fileInput.files.length) {
                alert('请选择视频文件');
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            formData.append('directory', directory);
            
            // 重置进度条
            progressBar.style.width = '0%';
            result.style.display = 'none';
            videoPreview.style.display = 'none';
            
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', function(event) {
                if (event.lengthComputable) {
                    const percentComplete = (event.loaded / event.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }
            });
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        resultContent.textContent = JSON.stringify(response, null, 2);
                        result.style.display = 'block';
                        
                        if (response.code === 1 && response.data && response.data.url) {
                            videoPreview.src = response.data.url;
                            videoPreview.style.display = 'block';
                            uploadedObjectKey = response.data.objectKey;
                        }
                    } else {
                        alert('上传失败: ' + xhr.statusText);
                    }
                }
            };
            
            xhr.open('POST', '/api/video/upload', true);
            xhr.send(formData);
        }
        
        function deleteVideo() {
            if (!uploadedObjectKey) {
                alert('没有可删除的视频');
                return;
            }
            
            const xhr = new XMLHttpRequest();
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        alert(response.msg);
                        if (response.code === 1) {
                            document.getElementById('result').style.display = 'none';
                            document.getElementById('videoPreview').style.display = 'none';
                            uploadedObjectKey = '';
                        }
                    } else {
                        alert('删除失败: ' + xhr.statusText);
                    }
                }
            };
            
            xhr.open('DELETE', `/api/video/delete?objectKey=${encodeURIComponent(uploadedObjectKey)}`, true);
            xhr.send();
        }
        
        function getPresignedUrl() {
            const objectKey = document.getElementById('objectKey').value;
            const expireTime = document.getElementById('expireTime').value;
            const presignedResult = document.getElementById('presignedResult');
            const presignedUrlContent = document.getElementById('presignedUrlContent');
            
            if (!objectKey) {
                alert('请输入对象键');
                return;
            }
            
            const xhr = new XMLHttpRequest();
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        presignedUrlContent.textContent = response.data;
                        presignedResult.style.display = 'block';
                        
                        if (response.code === 1 && response.data) {
                            presignedUrl = response.data;
                            document.getElementById('uploadPresignedBtn').disabled = false;
                        }
                    } else {
                        alert('获取预签名URL失败: ' + xhr.statusText);
                    }
                }
            };
            
            xhr.open('GET', `/api/video/presigned-url?objectKey=${encodeURIComponent(objectKey)}&expireTime=${expireTime}`, true);
            xhr.send();
        }
        
        function uploadWithPresignedUrl() {
            const fileInput = document.getElementById('presignedFile');
            const progressBar = document.getElementById('presignedProgressBar');
            const presignedResultContent = document.getElementById('presignedResultContent');
            
            if (!fileInput.files.length) {
                alert('请选择视频文件');
                return;
            }
            
            if (!presignedUrl) {
                alert('请先获取预签名URL');
                return;
            }
            
            const file = fileInput.files[0];
            
            // 重置进度条
            progressBar.style.width = '0%';
            
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            xhr.upload.addEventListener('progress', function(event) {
                if (event.lengthComputable) {
                    const percentComplete = (event.loaded / event.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                }
            });
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        presignedResultContent.textContent = '上传成功！\n\nHTTP状态码: ' + xhr.status;
                    } else {
                        presignedResultContent.textContent = '上传失败！\n\nHTTP状态码: ' + xhr.status + '\n错误: ' + xhr.statusText;
                    }
                }
            };
            
            xhr.open('PUT', presignedUrl, true);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.send(file);
        }
    </script>
</body>
</html> 