package com.miniot.wuyou.controller;

import com.miniot.wuyou.common.R;
import com.miniot.wuyou.service.OSSService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * 视频上传控制器
 */
@RestController
@RequestMapping("/api/video")
@Slf4j
public class VideoController {

    @Autowired
    private OSSService ossService;

    /**
     * 上传视频文件
     */
    @PostMapping("/upload")
    public R<Map<String, Object>> uploadVideo(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "directory", defaultValue = "videos") String directory) {
        
        try {
            log.info("开始上传视频文件: {}, 大小: {}", file.getOriginalFilename(), file.getSize());
            Map<String, Object> result = ossService.uploadVideo(file, directory);
            return R.success(result, "视频上传成功");
        } catch (IOException e) {
            log.error("视频上传失败: {}", e.getMessage(), e);
            return R.error("视频上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除视频文件
     */
    @DeleteMapping("/delete")
    public R<Boolean> deleteVideo(
            @RequestParam("objectKey") String objectKey) {
        
        boolean result = ossService.deleteFile(objectKey);
        return result ? 
            R.success(true, "视频删除成功") : 
            R.error("视频删除失败");
    }

    /**
     * 获取视频URL
     */
    @GetMapping("/url")
    public R<String> getVideoUrl(
            @RequestParam("objectKey") String objectKey) {
        
        String url = ossService.getFileUrl(objectKey);
        return R.success(url, "获取视频URL成功");
    }
    
    /**
     * 获取预签名上传URL
     */
    @GetMapping("/presigned-url")
    public R<String> getPresignedUrl(
            @RequestParam("objectKey") String objectKey,
            @RequestParam(value = "expireTime", defaultValue = "3600") long expireTime) {
        
        String url = ossService.generatePresignedUrl(objectKey, expireTime);
        return R.success(url, "获取预签名URL成功");
    }
} 