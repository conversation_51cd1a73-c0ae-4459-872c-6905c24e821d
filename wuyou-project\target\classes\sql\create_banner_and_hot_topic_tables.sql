-- 轮播图表
CREATE TABLE IF NOT EXISTS `banner` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '轮播图ID',
  `app_id` varchar(50) DEFAULT NULL COMMENT '应用ID',
  `title` varchar(100) DEFAULT NULL COMMENT '标题（非必填）',
  `subtitle` varchar(200) DEFAULT NULL COMMENT '副标题（非必填）',
  `image` varchar(255) NOT NULL COMMENT '图片地址',
  `link` varchar(255) DEFAULT NULL COMMENT '链接地址（非必填）',
  `link_type` varchar(20) NOT NULL DEFAULT 'page' COMMENT '链接类型：wxmini-小程序路径，link-外部链接，page-内部页面，none-无链接',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序值（数字越小越靠前）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-显示，0-隐藏',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='首页轮播图表';

-- 热门话题表
CREATE TABLE IF NOT EXISTS `hot_topic` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '热门话题ID',
  `app_id` varchar(50) DEFAULT NULL COMMENT '应用ID',
  `title` varchar(100) DEFAULT NULL COMMENT '话题名称（非必填）',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `link_url` varchar(255) DEFAULT NULL COMMENT '跳转链接（非必填）',
  `link_type` varchar(20) NOT NULL DEFAULT 'page' COMMENT '链接类型：wxmini-小程序路径，link-外部链接，page-内部页面，none-无链接',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序（数字越小越靠前）',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort_order`),
  KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='热门话题表'; 