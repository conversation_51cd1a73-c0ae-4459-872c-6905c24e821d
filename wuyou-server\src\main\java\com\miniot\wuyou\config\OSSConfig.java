package com.miniot.wuyou.config;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class OSSConfig {

    @Value("${oss.accessKey}")
    private String accessKey;

    @Value("${oss.secretKey}")
    private String secretKey;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${oss.bucketUrl}")
    private String bucketUrl;

    @Bean
    public AmazonS3Client amazonS3Client() {
        // 创建AmazonS3Client对象
        BasicAWSCredentials basicAwsCred = new BasicAWSCredentials(access<PERSON><PERSON>, secret<PERSON>ey);
        ClientConfiguration clientConfiguration = new ClientConfiguration();
        clientConfiguration.setProtocol(Protocol.HTTPS);
        
        AmazonS3Client s3Client = new AmazonS3Client(basicAwsCred, clientConfiguration);
        
        // 设置客户端选项
        S3ClientOptions options = S3ClientOptions.builder()
                .setPathStyleAccess(true)
                .setPayloadSigningEnabled(true)
                .disableChunkedEncoding()
                .build();
        s3Client.setS3ClientOptions(options);
        
        // 设置终端节点
        s3Client.setEndpoint("https://" + endpoint);
        
        return s3Client;
    }
} 