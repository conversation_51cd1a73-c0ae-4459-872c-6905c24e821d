# 五有视频上传服务

基于Spring Boot和联通云对象存储OSS实现的视频上传和存储服务。

## 功能特性

- 视频文件上传到联通云OSS
- 视频文件删除
- 获取视频访问URL
- 支持预签名URL上传
- 支持大文件上传（最大500MB）
- 按日期自动生成存储路径
- 生成唯一文件名，避免文件覆盖
- 提供简单的前端测试页面

## 技术栈

- Spring Boot 2.7.5
- Amazon S3 Java SDK 1.11.24 (兼容联通云OSS)
- Commons FileUpload 1.4
- Lombok

## 配置说明

在`application.yml`中配置联通云OSS参数：

```yaml
oss:
  accessKey: E60CDB1EF6DA4EE79E9AB48545DACB073102
  secretKey: 0D39978EE5ED4292BD8BA1D4FD843CC64792
  endpoint: obs-cq.cucloud.cn
  bucketUrl: https://xiangcunzhengxing.obs-cq.cucloud.cn
  bucketName: xiangcunzhengxing
```

## API接口

### 上传视频

- **URL**: `/api/video/upload`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **参数**:
  - `file`: 视频文件（必填）
  - `directory`: 存储目录（可选，默认为"videos"）
- **响应**:
  ```json
  {
    "code": 1,
    "msg": "视频上传成功",
    "data": {
      "url": "https://xiangcunzhengxing.obs-cq.cucloud.cn/videos/2025/07/28/abcdef.mp4",
      "objectKey": "videos/2025/07/28/abcdef.mp4",
      "fileName": "example.mp4",
      "fileSize": 1024000
    }
  }
  ```

### 删除视频

- **URL**: `/api/video/delete`
- **Method**: DELETE
- **参数**:
  - `objectKey`: 对象键（必填，例如："videos/2025/07/28/abcdef.mp4"）
- **响应**:
  ```json
  {
    "code": 1,
    "msg": "视频删除成功",
    "data": true
  }
  ```

### 获取视频URL

- **URL**: `/api/video/url`
- **Method**: GET
- **参数**:
  - `objectKey`: 对象键（必填，例如："videos/2025/07/28/abcdef.mp4"）
- **响应**:
  ```json
  {
    "code": 1,
    "msg": "获取视频URL成功",
    "data": "https://xiangcunzhengxing.obs-cq.cucloud.cn/videos/2025/07/28/abcdef.mp4"
  }
  ```

### 获取预签名URL

- **URL**: `/api/video/presigned-url`
- **Method**: GET
- **参数**:
  - `objectKey`: 对象键（必填，例如："videos/test.mp4"）
  - `expireTime`: 过期时间（秒，可选，默认3600）
- **响应**:
  ```json
  {
    "code": 1,
    "msg": "获取预签名URL成功",
    "data": "https://xiangcunzhengxing.obs-cq.cucloud.cn/videos/test.mp4?AWSAccessKeyId=xxx&Expires=xxx&Signature=xxx"
  }
  ```

## 使用方法

1. 启动服务：`mvn spring-boot:run`
2. 访问测试页面：`http://localhost:8080/upload.html`
3. 选择视频文件并上传
4. 上传成功后可以预览视频并获取URL

## 注意事项

- 视频文件大小限制为500MB
- 支持常见视频格式：MP4, AVI, WMV, FLV, MOV, MKV等
- 上传后的视频会按照日期自动分类存储
- 文件名会自动生成UUID，避免重名覆盖 